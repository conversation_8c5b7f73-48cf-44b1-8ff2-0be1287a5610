// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/collapse/demo/accordion.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
  role="tablist"
>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="tab"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="tab"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="tab"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/accordion.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
>
  <div
    class="ant-collapse-item ant-collapse-item-active"
  >
    <div
      aria-disabled="false"
      aria-expanded="true"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="expanded"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            style="transform: rotate(90deg);"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
    <div
      class="ant-collapse-panel ant-collapse-panel-active"
    >
      <div
        class="ant-collapse-body"
      >
        <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
        </p>
      </div>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/borderless.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start ant-collapse-borderless css-var-test-id"
>
  <div
    class="ant-collapse-item ant-collapse-item-active"
  >
    <div
      aria-disabled="false"
      aria-expanded="true"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="expanded"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            style="transform: rotate(90deg);"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
    <div
      class="ant-collapse-panel ant-collapse-panel-active"
    >
      <div
        class="ant-collapse-body"
      >
        <p
          style="padding-inline-start: 24px;"
        >
          A dog is a type of domesticated animal. Known for its loyalty and faithfulness, it can be found as a welcome guest in many households across the world.
        </p>
      </div>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/borderless.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/collapsible.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
    >
      <div
        class="ant-collapse-item ant-collapse-item-active"
      >
        <div
          class="ant-collapse-header ant-collapse-collapsible-header"
        >
          <div
            aria-disabled="false"
            aria-expanded="true"
            class="ant-collapse-expand-icon"
            role="button"
            tabindex="0"
          >
            <span
              aria-label="expanded"
              class="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style="transform: rotate(90deg);"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            aria-disabled="false"
            aria-expanded="true"
            class="ant-collapse-title"
            role="button"
            tabindex="0"
          >
            This panel can be collapsed by clicking text or icon
          </span>
        </div>
        <div
          class="ant-collapse-panel ant-collapse-panel-active"
        >
          <div
            class="ant-collapse-body"
          >
            <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
    >
      <div
        class="ant-collapse-item ant-collapse-item-active"
      >
        <div
          class="ant-collapse-header ant-collapse-collapsible-icon"
        >
          <div
            aria-disabled="false"
            aria-expanded="true"
            class="ant-collapse-expand-icon"
            role="button"
            tabindex="0"
          >
            <span
              aria-label="expanded"
              class="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style="transform: rotate(90deg);"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title"
          >
            This panel can only be collapsed by clicking icon
          </span>
        </div>
        <div
          class="ant-collapse-panel ant-collapse-panel-active"
        >
          <div
            class="ant-collapse-body"
          >
            <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
    >
      <div
        class="ant-collapse-item ant-collapse-item-disabled"
      >
        <div
          aria-disabled="true"
          aria-expanded="false"
          class="ant-collapse-header ant-collapse-collapsible-disabled"
          role="button"
          tabindex="-1"
        >
          <div
            class="ant-collapse-expand-icon"
          >
            <span
              aria-label="collapsed"
              class="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title"
          >
            This panel can't be collapsed
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/collapsible.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/component-token.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1, (Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! )
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2, (Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! )
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3, (Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! Ant Design! )
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/custom.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start ant-collapse-borderless css-var-test-id"
  style="background: rgb(255, 255, 255);"
>
  <div
    class="ant-collapse-item ant-collapse-item-active"
    style="margin-bottom: 24px; background: rgba(0, 0, 0, 0.02); border-radius: 8px;"
  >
    <div
      aria-disabled="false"
      aria-expanded="true"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="caret-right"
          class="anticon anticon-caret-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="caret-right"
            fill="currentColor"
            focusable="false"
            height="1em"
            style="transform: rotate(90deg);"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
    <div
      class="ant-collapse-panel ant-collapse-panel-active"
    >
      <div
        class="ant-collapse-body"
      >
        <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
        </p>
      </div>
    </div>
  </div>
  <div
    class="ant-collapse-item"
    style="margin-bottom: 24px; background: rgba(0, 0, 0, 0.02); border-radius: 8px;"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="caret-right"
          class="anticon anticon-caret-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="caret-right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
    style="margin-bottom: 24px; background: rgba(0, 0, 0, 0.02); border-radius: 8px;"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="caret-right"
          class="anticon anticon-caret-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="caret-right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M715.8 493.5L335 165.1c-14.2-12.2-35-1.2-35 18.5v656.8c0 19.7 20.8 30.7 35 18.5l380.8-328.4c10.9-9.4 10.9-27.6 0-37z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/custom.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/extra.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
  >
    <div
      class="ant-collapse-item ant-collapse-item-active"
    >
      <div
        aria-disabled="false"
        aria-expanded="true"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="expanded"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              style="transform: rotate(90deg);"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is panel header 1
        </span>
        <div
          class="ant-collapse-extra"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
            tabindex="-1"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
      <div
        class="ant-collapse-panel ant-collapse-panel-active"
      >
        <div
          class="ant-collapse-body"
        >
          <div>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-collapse-item"
    >
      <div
        aria-disabled="false"
        aria-expanded="false"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="collapsed"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is panel header 2
        </span>
        <div
          class="ant-collapse-extra"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
            tabindex="-1"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
    <div
      class="ant-collapse-item"
    >
      <div
        aria-disabled="false"
        aria-expanded="false"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="collapsed"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is panel header 3
        </span>
        <div
          class="ant-collapse-extra"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
            tabindex="-1"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </div>
      </div>
    </div>
  </div>,
  <br />,
  <span>
    Expand Icon Placement: 
  </span>,
  <div
    class="ant-select ant-select-outlined css-var-test-id ant-select-css-var ant-select-single ant-select-show-arrow"
    style="margin: 0px 8px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="test-id_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="test-id_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="test-id"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-item"
          title="start"
        >
          start
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up css-var-test-id ant-select-css-var ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div>
        <div
          id="test-id_list"
          role="listbox"
          style="height: 0px; width: 0px; overflow: hidden;"
        >
          <div
            aria-label="start"
            aria-selected="true"
            id="test-id_list_0"
            role="option"
          >
            start
          </div>
          <div
            aria-label="end"
            aria-selected="false"
            id="test-id_list_1"
            role="option"
          >
            end
          </div>
        </div>
        <div
          class="rc-virtual-list"
          style="position: relative;"
        >
          <div
            class="rc-virtual-list-holder"
            style="max-height: 256px; overflow-y: auto; overflow-anchor: none;"
          >
            <div>
              <div
                class="rc-virtual-list-holder-inner"
                style="display: flex; flex-direction: column;"
              >
                <div
                  class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                  title="start"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    start
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
                <div
                  class="ant-select-item ant-select-item-option"
                  title="end"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    end
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
]
`;

exports[`renders components/collapse/demo/extra.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/ghost.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start ant-collapse-ghost css-var-test-id"
>
  <div
    class="ant-collapse-item ant-collapse-item-active"
  >
    <div
      aria-disabled="false"
      aria-expanded="true"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="expanded"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            style="transform: rotate(90deg);"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
    <div
      class="ant-collapse-panel ant-collapse-panel-active"
    >
      <div
        class="ant-collapse-body"
      >
        <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
        </p>
      </div>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/ghost.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/mix.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 1
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 2
      </span>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="collapsed"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header 3
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/mix.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/noarrow.tsx extend context correctly 1`] = `
<div
  class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
>
  <div
    class="ant-collapse-item ant-collapse-item-active"
  >
    <div
      aria-disabled="false"
      aria-expanded="true"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <div
        class="ant-collapse-expand-icon"
      >
        <span
          aria-label="expanded"
          class="anticon anticon-right ant-collapse-arrow"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="right"
            fill="currentColor"
            focusable="false"
            height="1em"
            style="transform: rotate(90deg);"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
            />
          </svg>
        </span>
      </div>
      <span
        class="ant-collapse-title"
      >
        This is panel header with arrow icon
      </span>
    </div>
    <div
      class="ant-collapse-panel ant-collapse-panel-active"
    >
      <div
        class="ant-collapse-body"
      >
        <p>
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
        </p>
      </div>
    </div>
  </div>
  <div
    class="ant-collapse-item"
  >
    <div
      aria-disabled="false"
      aria-expanded="false"
      class="ant-collapse-header"
      role="button"
      tabindex="0"
    >
      <span
        class="ant-collapse-title"
      >
        This is panel header with no arrow icon
      </span>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/noarrow.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/size.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Default Size
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-collapse ant-collapse-icon-placement-start css-var-test-id"
  >
    <div
      class="ant-collapse-item"
    >
      <div
        aria-disabled="false"
        aria-expanded="false"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="collapsed"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is default size panel header
        </span>
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Small Size
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-collapse ant-collapse-icon-placement-start ant-collapse-small css-var-test-id"
  >
    <div
      class="ant-collapse-item"
    >
      <div
        aria-disabled="false"
        aria-expanded="false"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="collapsed"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is small size panel header
        </span>
      </div>
    </div>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Large Size
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-collapse ant-collapse-icon-placement-start ant-collapse-large css-var-test-id"
  >
    <div
      class="ant-collapse-item"
    >
      <div
        aria-disabled="false"
        aria-expanded="false"
        class="ant-collapse-header"
        role="button"
        tabindex="0"
      >
        <div
          class="ant-collapse-expand-icon"
        >
          <span
            aria-label="collapsed"
            class="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
        </div>
        <span
          class="ant-collapse-title"
        >
          This is large size panel header
        </span>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/collapse/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/collapse/demo/style-class.tsx extend context correctly 1`] = `
<div
  style="display: flex; flex-direction: column; gap: 24px;"
>
  <div>
    <h4>
      Static classNames and styles
    </h4>
    <div
      class="ant-collapse ant-collapse-icon-placement-start css-var-test-id custom-collapse-root"
      style="background-color: rgb(250, 250, 250); border: 1px solid rgb(224, 224, 224); border-radius: 8px;"
    >
      <div
        class="ant-collapse-item ant-collapse-item-active"
      >
        <div
          aria-disabled="false"
          aria-expanded="true"
          class="ant-collapse-header custom-collapse-header"
          role="button"
          style="background-color: rgb(240, 240, 240); padding: 12px 16px; font-weight: bold;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon custom-collapse-icon"
            style="color: rgb(82, 196, 26); font-size: 16px;"
          >
            <span
              aria-label="expanded"
              class="anticon anticon-right custom-collapse-icon ant-collapse-arrow"
              role="img"
              style="color: rgb(82, 196, 26); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style="transform: rotate(90deg);"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title custom-collapse-title"
            style="color: rgb(24, 144, 255);"
          >
            This is panel header 1
          </span>
        </div>
        <div
          class="ant-collapse-panel ant-collapse-panel-active"
        >
          <div
            class="ant-collapse-body custom-collapse-body"
            style="background-color: rgb(255, 255, 255); padding: 16px;"
          >
            <p>
              A dog is a type of domesticated animal. Known for its loyalty and faithfulness, it can be found as a welcome guest in many households across the world.
            </p>
          </div>
        </div>
      </div>
      <div
        class="ant-collapse-item"
      >
        <div
          aria-disabled="false"
          aria-expanded="false"
          class="ant-collapse-header custom-collapse-header"
          role="button"
          style="background-color: rgb(240, 240, 240); padding: 12px 16px; font-weight: bold;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon custom-collapse-icon"
            style="color: rgb(82, 196, 26); font-size: 16px;"
          >
            <span
              aria-label="collapsed"
              class="anticon anticon-right custom-collapse-icon ant-collapse-arrow"
              role="img"
              style="color: rgb(82, 196, 26); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title custom-collapse-title"
            style="color: rgb(24, 144, 255);"
          >
            This is panel header 2
          </span>
        </div>
      </div>
      <div
        class="ant-collapse-item"
      >
        <div
          aria-disabled="false"
          aria-expanded="false"
          class="ant-collapse-header custom-collapse-header"
          role="button"
          style="background-color: rgb(240, 240, 240); padding: 12px 16px; font-weight: bold;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon custom-collapse-icon"
            style="color: rgb(82, 196, 26); font-size: 16px;"
          >
            <span
              aria-label="collapsed"
              class="anticon anticon-right custom-collapse-icon ant-collapse-arrow"
              role="img"
              style="color: rgb(82, 196, 26); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title custom-collapse-title"
            style="color: rgb(24, 144, 255);"
          >
            This is panel header 3
          </span>
        </div>
      </div>
    </div>
  </div>
  <div>
    <h4>
      Function-based classNames and styles
    </h4>
    <div
      class="ant-collapse ant-collapse-icon-placement-end ant-collapse-ghost ant-collapse-large css-var-test-id dynamic-collapse-large"
      style="border-width: 2px; opacity: 0.8;"
    >
      <div
        class="ant-collapse-item"
      >
        <div
          aria-disabled="false"
          aria-expanded="false"
          class="ant-collapse-header dynamic-header"
          role="button"
          style="font-size: 16px;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon end-icon"
            style="transform: rotate(180deg);"
          >
            <span
              aria-label="collapsed"
              class="anticon anticon-right end-icon ant-collapse-arrow"
              role="img"
              style="transform: rotate(180deg);"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title ghost-title"
            style="font-weight: bold;"
          >
            This is panel header 1
          </span>
        </div>
      </div>
      <div
        class="ant-collapse-item ant-collapse-item-active"
      >
        <div
          aria-disabled="false"
          aria-expanded="true"
          class="ant-collapse-header dynamic-header"
          role="button"
          style="font-size: 16px;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon end-icon"
            style="transform: rotate(180deg);"
          >
            <span
              aria-label="expanded"
              class="anticon anticon-right end-icon ant-collapse-arrow"
              role="img"
              style="transform: rotate(180deg);"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style="transform: rotate(90deg);"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title ghost-title"
            style="font-weight: bold;"
          >
            This is panel header 2
          </span>
        </div>
        <div
          class="ant-collapse-panel ant-collapse-panel-active"
        >
          <div
            class="ant-collapse-body dynamic-body"
            style="font-size: 14px;"
          >
            <p>
              A dog is a type of domesticated animal. Known for its loyalty and faithfulness, it can be found as a welcome guest in many households across the world.
            </p>
          </div>
        </div>
      </div>
      <div
        class="ant-collapse-item"
      >
        <div
          aria-disabled="false"
          aria-expanded="false"
          class="ant-collapse-header dynamic-header"
          role="button"
          style="font-size: 16px;"
          tabindex="0"
        >
          <div
            class="ant-collapse-expand-icon end-icon"
            style="transform: rotate(180deg);"
          >
            <span
              aria-label="collapsed"
              class="anticon anticon-right end-icon ant-collapse-arrow"
              role="img"
              style="transform: rotate(180deg);"
            >
              <svg
                aria-hidden="true"
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
          </div>
          <span
            class="ant-collapse-title ghost-title"
            style="font-weight: bold;"
          >
            This is panel header 3
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/collapse/demo/style-class.tsx extend context correctly 2`] = `[]`;
