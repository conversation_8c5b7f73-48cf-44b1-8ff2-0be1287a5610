## zh-CN

通过 `classNames` 和 `styles` 属性为 Image 组件的各个部分设置样式。

- `classNames` 和 `styles` 支持对象和函数两种形式
- 对象形式：直接设置各个语义化 DOM 的类名和样式
- 函数形式：`(info: { props }) => Record<SemanticDOM, string | CSSProperties>`，可以根据组件的 props 动态设置样式

### 语义化 DOM 结构

- `root`：最外层容器
- `image`：图片元素
- `cover`：预览遮罩层
- `popup.root`：预览弹窗根容器
- `popup.mask`：预览弹窗遮罩
- `popup.body`：预览弹窗主体
- `popup.footer`：预览弹窗底部
- `popup.actions`：预览弹窗操作按钮区域

## en-US

Set styles for different parts of the Image component through `classNames` and `styles` properties.

- `classNames` and `styles` support both object and function forms
- Object form: directly set class names and styles for each semantic DOM
- Function form: `(info: { props }) => Record<SemanticDOM, string | CSSProperties>`, can dynamically set styles based on component props

### Semantic DOM Structure

- `root`: Outermost container
- `image`: Image element
- `cover`: Preview mask layer
- `popup.root`: Preview popup root container
- `popup.mask`: Preview popup mask
- `popup.body`: Preview popup body
- `popup.footer`: Preview popup footer
- `popup.actions`: Preview popup action buttons area
