// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/popover/demo/arrow.tsx extend context correctly 1`] = `
Array [
  <div
    aria-label="segmented control"
    class="ant-segmented css-var-test-id"
    role="radiogroup"
    style="margin-bottom: 24px;"
    tabindex="0"
  >
    <div
      class="ant-segmented-group"
    >
      <label
        class="ant-segmented-item ant-segmented-item-selected"
      >
        <input
          checked=""
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="true"
          class="ant-segmented-item-label"
          role="radio"
          title="Show"
        >
          Show
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="Hide"
        >
          Hide
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="Center"
        >
          Center
        </div>
      </label>
    </div>
  </div>,
  <div
    class="demo ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-vertical"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="white-space: nowrap;"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          TL
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-topLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          Top
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          TR
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-topRight"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
      style="width: 432px;"
    >
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
      >
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            LT
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-leftTop"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            Left
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-left"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute; top: 0px; right: 0px;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            LB
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-leftBottom"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
      >
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            RT
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-rightTop"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            Right
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-right"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute; top: 0px; left: 0px;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button
          aria-describedby="test-id"
          class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px; margin: 4px;"
          type="button"
        >
          <span>
            RB
          </span>
        </button>
        <div
          class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-rightBottom"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
        >
          <div
            class="ant-popover-arrow"
            style="position: absolute;"
          />
          <div
            class="ant-popover-content"
          >
            <div
              class="ant-popover-inner"
              id="test-id"
              role="tooltip"
            >
              <div
                class="ant-popover-title"
              >
                <span>
                  Title
                </span>
              </div>
              <div
                class="ant-popover-inner-content"
              >
                <div>
                  <p>
                    Content
                  </p>
                  <p>
                    Content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
      style="white-space: nowrap;"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          BL
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          Bottom
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottom"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          BR
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottomRight"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/arrow.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/arrow-point-at-center.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-wrap-wrap"
  style="gap: 16px;"
>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-topLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              topLeft
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              top
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-topRight"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              topRight
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-leftTop"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              leftTop
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-left"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; top: 0px; right: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              left
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-leftBottom"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              leftBottom
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-rightTop"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              rightTop
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-right"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              right
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-rightBottom"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              rightBottom
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              bottomLeft
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-bottom"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              bottom
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="acss-6kvo78"
  >
    <div
      class="acss-1nif5xu ant-popover-open"
    />
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-bottomRight"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          role="tooltip"
        >
          <div
            class="ant-popover-inner-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-center"
            >
              bottomRight
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popover/demo/arrow-point-at-center.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/basic.tsx extend context correctly 1`] = `
Array [
  <button
    aria-describedby="test-id"
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Hover me
    </span>
  </button>,
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        id="test-id"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/component-token.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-bottomLeft css-var-test-id"
    style="width: 250px;"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
      style="width: 250px;"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/component-token.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/control.tsx extend context correctly 1`] = `
Array [
  <button
    aria-describedby="test-id"
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Click me
    </span>
  </button>,
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        id="test-id"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <a>
            Close
          </a>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/control.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/hover-with-click.tsx extend context correctly 1`] = `
Array [
  <button
    aria-describedby="test-id"
    class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Hover and click / 悬停并单击
    </span>
  </button>,
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; z-index: 1200;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        id="test-id"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Click title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <div>
              This is click content.
            </div>
            <a>
              Close
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        id="test-id"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Hover title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            This is hover content.
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/hover-with-click.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/placement.tsx extend context correctly 1`] = `
<div
  class="demo ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
    style="white-space: nowrap;"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        TL
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-topLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        Top
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        TR
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-topRight"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-space-between"
    style="width: 432px;"
  >
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          LT
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-leftTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          Left
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-left"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute; top: 0px; right: 0px;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          LB
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-leftBottom"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-align-center ant-flex-vertical"
    >
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          RT
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-rightTop"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          Right
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-right"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute; top: 0px; left: 0px;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        aria-describedby="test-id"
        class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
        style="width: 80px; margin: 4px;"
        type="button"
      >
        <span>
          RB
        </span>
      </button>
      <div
        class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-rightBottom"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
      >
        <div
          class="ant-popover-arrow"
          style="position: absolute;"
        />
        <div
          class="ant-popover-content"
        >
          <div
            class="ant-popover-inner"
            id="test-id"
            role="tooltip"
          >
            <div
              class="ant-popover-title"
            >
              <span>
                Title
              </span>
            </div>
            <div
              class="ant-popover-inner-content"
            >
              <div>
                <p>
                  Content
                </p>
                <p>
                  Content
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-align-center ant-flex-justify-center"
    style="white-space: nowrap;"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        BL
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        Bottom
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottom"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; top: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      style="width: 80px; margin: 4px;"
      type="button"
    >
      <span>
        BR
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-bottomRight"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            <span>
              Title
            </span>
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popover/demo/placement.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/render-panel.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-bottomLeft css-var-test-id"
    style="width: 250px;"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
      style="width: 250px;"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/render-panel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/shift.tsx extend context correctly 1`] = `
<div
  style="width: 300vw; height: 300vh; display: flex; align-items: center; justify-content: center;"
>
  <button
    class="ant-btn ant-btn-primary ant-popover-open"
    type="button"
  >
    <span>
      Scroll The Window
    </span>
  </button>
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          Thanks for using antd. Have a nice day !
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popover/demo/shift.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-large ant-space-gap-col-large css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        对象形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              点击显示 Popover
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id custom-popover-root ant-popover-placement-top"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(24, 144, 255); border-radius: 8px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; bottom: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner custom-popover-body"
                id="test-id"
                role="tooltip"
                style="padding: 16px; background-color: rgb(240, 248, 255); border-radius: 6px;"
              >
                <div
                  class="ant-popover-title"
                >
                  标题
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  <div>
                    <p>
                      这是内容
                    </p>
                    <p>
                      这是更多内容
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              悬停显示 Popover
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id custom-popover-hover ant-popover-placement-top"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(82, 196, 26); border-radius: 12px; background-color: rgba(82, 196, 26, 0.1);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; bottom: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner custom-popover-hover-body"
                id="test-id"
                role="tooltip"
                style="padding: 12px; color: rgb(82, 196, 26); font-weight: bold;"
              >
                <div
                  class="ant-popover-title"
                >
                  不同样式
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  这是另一个 Popover
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        函数形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
            type="button"
          >
            <span>
              点击触发动态样式
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id click-popover ant-popover-placement-bottom"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(255, 77, 79); border-radius: 8px 8px 0 0; background-color: rgba(255, 77, 79, 0.1); transform: translateY(-2px);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner dynamic-body-bottom"
                id="test-id"
                role="tooltip"
                style="padding: 20px; color: rgb(255, 77, 79); font-size: 14px; text-align: center;"
              >
                <div
                  class="ant-popover-title"
                >
                  动态样式
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  <div>
                    <p>
                      这是内容
                    </p>
                    <p>
                      这是更多内容
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
            type="button"
          >
            <span>
              悬停触发动态样式
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id hover-dynamic-right ant-popover-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(114, 46, 209); border-radius: 0 8px 8px 0; background-color: rgba(114, 46, 209, 0.1); min-width: 200px;"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner hover-body"
                id="test-id"
                role="tooltip"
                style="padding: 16px 16px 16px 20px; color: rgb(114, 46, 209); border-left: 3px solid currentcolor;"
              >
                <div
                  class="ant-popover-title"
                >
                  悬停动态样式
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  根据 props 动态变化的样式
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        不同位置的 Popover 样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              top
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id placement-top ant-popover-placement-top"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(24, 144, 255); border-radius: 6px; background-color: rgba(24, 144, 255, 0.063);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; bottom: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner body-top"
                id="test-id"
                role="tooltip"
                style="padding: 12px; color: rgb(24, 144, 255); font-weight: 500; text-align: center;"
              >
                <div
                  class="ant-popover-title"
                >
                  top 位置
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  这是 top 位置的 Popover
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              left
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id placement-left ant-popover-placement-left"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(245, 34, 45); border-radius: 6px; background-color: rgba(245, 34, 45, 0.063);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; top: 0px; right: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner body-left"
                id="test-id"
                role="tooltip"
                style="padding: 12px; color: rgb(245, 34, 45); font-weight: 500; text-align: center;"
              >
                <div
                  class="ant-popover-title"
                >
                  left 位置
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  这是 left 位置的 Popover
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              right
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id placement-right ant-popover-placement-right"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(82, 196, 26); border-radius: 6px; background-color: rgba(82, 196, 26, 0.063);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner body-right"
                id="test-id"
                role="tooltip"
                style="padding: 12px; color: rgb(82, 196, 26); font-weight: 500; text-align: center;"
              >
                <div
                  class="ant-popover-title"
                >
                  right 位置
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  这是 right 位置的 Popover
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            aria-describedby="test-id"
            class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              bottom
            </span>
          </button>
          <div
            class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id placement-bottom ant-popover-placement-bottom"
            style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box; border: 2px solid rgb(250, 173, 20); border-radius: 6px; background-color: rgba(250, 173, 20, 0.063);"
          >
            <div
              class="ant-popover-arrow"
              style="position: absolute; top: 0px; left: 0px;"
            />
            <div
              class="ant-popover-content"
            >
              <div
                class="ant-popover-inner body-bottom"
                id="test-id"
                role="tooltip"
                style="padding: 12px; color: rgb(250, 173, 20); font-weight: 500; text-align: center;"
              >
                <div
                  class="ant-popover-title"
                >
                  bottom 位置
                </div>
                <div
                  class="ant-popover-inner-content"
                >
                  这是 bottom 位置的 Popover
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popover/demo/style-class.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Space] \`direction\` is deprecated. Please use \`orientation\` instead.",
]
`;

exports[`renders components/popover/demo/triggerType.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  style="flex-wrap: wrap;"
>
  <div
    class="ant-space-item"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Hover me
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            Title
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Focus me
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            Title
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <button
      aria-describedby="test-id"
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
      type="button"
    >
      <span>
        Click me
      </span>
    </button>
    <div
      class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
    >
      <div
        class="ant-popover-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-popover-content"
      >
        <div
          class="ant-popover-inner"
          id="test-id"
          role="tooltip"
        >
          <div
            class="ant-popover-title"
          >
            Title
          </div>
          <div
            class="ant-popover-inner-content"
          >
            <div>
              <p>
                Content
              </p>
              <p>
                Content
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/popover/demo/triggerType.tsx extend context correctly 2`] = `[]`;

exports[`renders components/popover/demo/wireframe.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-top css-var-test-id"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-popover ant-popover-pure ant-popover-placement-bottomLeft css-var-test-id"
    style="width: 250px;"
  >
    <div
      class="ant-popover-arrow"
    />
    <div
      class="ant-popover-content"
      style="width: 250px;"
    >
      <div
        class="ant-popover-inner"
        role="tooltip"
      >
        <div
          class="ant-popover-title"
        >
          Title
        </div>
        <div
          class="ant-popover-inner-content"
        >
          <div>
            <p>
              Content
            </p>
            <p>
              Content
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/popover/demo/wireframe.tsx extend context correctly 2`] = `[]`;
