import React from 'react';

import Popover from '..';
import mountTest from '../../../tests/shared/mountTest';
import { fireEvent, render } from '../../../tests/utils';
import ConfigProvider from '../../config-provider';
import type { TooltipRef } from '../../tooltip';

const { _InternalPanelDoNotUseOrYouWillBeFired: InternalPanelDoNotUseOrYouWillBeFired } = Popover;

describe('Popover', () => {
  mountTest(Popover);

  const eventObject = expect.objectContaining({
    target: expect.anything(),
    preventDefault: expect.any(Function),
  });

  it('should show overlay when trigger is clicked', () => {
    const ref = React.createRef<TooltipRef>();
    const { container } = render(
      <Popover ref={ref} content="console.log('hello world')" title="code" trigger="click">
        <span>show me your code</span>
      </Popover>,
    );
    expect(container.querySelector('.ant-popover-inner-content')).toBeFalsy();
    fireEvent.click(container.querySelector('span')!);
    expect(container.querySelector('.ant-popover-inner-content')).toBeTruthy();
  });

  it('should support defaultOpen', () => {
    const { container } = render(
      <Popover title="code" defaultOpen>
        <span>show me your code</span>
      </Popover>,
    );
    expect(container.querySelector('.ant-popover')).toBeTruthy();
  });

  it('shows content for render functions', () => {
    const renderTitle = () => 'some-title';
    const renderContent = () => 'some-content';
    const ref = React.createRef<TooltipRef>();
    const { container } = render(
      <Popover ref={ref} content={renderContent} title={renderTitle} trigger="click">
        <span>show me your code </span>
      </Popover>,
    );
    fireEvent.click(container.querySelector('span')!);
    const popup = document.querySelector('.ant-popover')!;
    expect(popup).not.toBe(null);
    expect(popup.innerHTML).toContain('some-title');
    expect(popup.innerHTML).toContain('some-content');
    expect(popup.innerHTML).toMatchSnapshot();
  });

  it('handles empty title/content props safely', () => {
    const { container } = render(
      <Popover trigger="click">
        <span>show me your code</span>
      </Popover>,
    );
    fireEvent.click(container.querySelector('span')!);

    const popup = document.querySelector('.ant-popover');
    expect(popup).toBe(null);
  });

  it('should not render popover when the title & content props is empty', () => {
    const { container } = render(
      <Popover trigger="click">
        <span>show me your code</span>
      </Popover>,
    );
    fireEvent.click(container.querySelector('span')!);

    const popup = document.querySelector('.ant-popover');
    expect(popup).toBe(null);
  });

  it('props#overlay do not warn anymore', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const overlay = jest.fn();
    render(
      <Popover content="console.log('hello world')" title="code" trigger="click">
        <span>show me your code</span>
      </Popover>,
    );

    expect(errorSpy).not.toHaveBeenCalled();
    expect(overlay).not.toHaveBeenCalled();
  });

  it(`should be rendered correctly in RTL direction`, () => {
    const { container } = render(
      <ConfigProvider direction="rtl">
        <Popover title="RTL" open>
          <span>show me your Rtl demo</span>
        </Popover>
      </ConfigProvider>,
    );
    expect(Array.from<Element>(container.children)).toMatchSnapshot();
  });

  it('should right work when content is null & title is null', () => {
    expect(() => {
      render(<InternalPanelDoNotUseOrYouWillBeFired content={null} title={null} trigger="click" />);
    }).not.toThrow();
  });

  it('should be closed by pressing ESC', () => {
    const onOpenChange = jest.fn((_, e) => {
      e?.persist?.();
    });
    const wrapper = render(
      <Popover title="Title" trigger="click" onOpenChange={onOpenChange}>
        <span>Delete</span>
      </Popover>,
    );
    const triggerNode = wrapper.container.querySelectorAll('span')[0];
    fireEvent.click(triggerNode);
    expect(onOpenChange).toHaveBeenLastCalledWith(true, undefined);
    fireEvent.keyDown(triggerNode, { key: 'Escape', keyCode: 27 });
    expect(onOpenChange).toHaveBeenLastCalledWith(false, eventObject);
  });

  it('should not display overlay when the content is null/undefined', () => {
    [null, undefined].forEach((item) => {
      const { container } = render(
        <Popover title={() => item} content={() => item} trigger="click">
          <span>show me your code</span>
        </Popover>,
      );
      fireEvent.click(container.querySelector<HTMLSpanElement>('span')!);
      const popup = document.querySelector('.ant-popover');
      expect(popup).toBe(null);
    });
  });

  it('should apply custom styles to Popover', () => {
    const customClassNames = {
      body: 'custom-body',
      root: 'custom-root',
    };

    const customStyles = {
      body: { color: 'red' },
      root: { backgroundColor: 'blue' },
    };

    const { container } = render(
      <Popover classNames={customClassNames} overlay={<div />} styles={customStyles} open>
        <button type="button">button</button>
      </Popover>,
    );

    const popoverElement = container.querySelector('.ant-popover') as HTMLElement;
    const popoverBodyElement = container.querySelector('.ant-popover-inner') as HTMLElement;

    // 验证 classNames
    expect(popoverElement.classList).toContain('custom-root');
    expect(popoverBodyElement.classList).toContain('custom-body');

    // 验证 styles
    expect(popoverElement.style.backgroundColor).toBe('blue');
    expect(popoverBodyElement.style.color).toBe('red');
  });
  it('ConfigProvider support arrow props', () => {
    const TooltipTestComponent = () => {
      const [configArrow, setConfigArrow] = React.useState(true);

      return (
        <ConfigProvider
          popover={{
            arrow: configArrow,
          }}
        >
          <button onClick={() => setConfigArrow(false)} className="configArrow" type="button">
            showconfigArrow
          </button>
          <Popover open>
            <div className="target">target</div>
          </Popover>
        </ConfigProvider>
      );
    };
    const { container } = render(<TooltipTestComponent />);
    const getTooltipArrow = () => container.querySelector('.ant-popover-arrow');
    const configbtn = container.querySelector('.configArrow');

    expect(getTooltipArrow()).not.toBeNull();
    fireEvent.click(configbtn!);
    expect(getTooltipArrow()).toBeNull();
  });
  it('ConfigProvider with arrow set to false, Tooltip arrow controlled by prop', () => {
    const TooltipTestComponent = () => {
      const [arrow, setArrow] = React.useState(true);

      return (
        <ConfigProvider
          popover={{
            arrow: false,
          }}
        >
          <button onClick={() => setArrow(!arrow)} className="toggleArrow" type="button">
            toggleArrow
          </button>
          <Popover open arrow={arrow}>
            <div className="target">target</div>
          </Popover>
        </ConfigProvider>
      );
    };

    const { container } = render(<TooltipTestComponent />);

    const getTooltipArrow = () => container.querySelector('.ant-popover-arrow');
    const toggleArrowBtn = container.querySelector('.toggleArrow');

    // Initial render, arrow should be visible because Tooltip's arrow prop is true
    expect(getTooltipArrow()).not.toBeNull();

    // Click the toggleArrow button to hide the arrow
    fireEvent.click(toggleArrowBtn!);
    expect(getTooltipArrow()).toBeNull();

    // Click the toggleArrow button again to show the arrow
    fireEvent.click(toggleArrowBtn!);
    expect(getTooltipArrow()).not.toBeNull();
  });

  it('support classNames and styles as objects', () => {
    const { container } = render(
      <Popover
        title="Test Title"
        content="Test Content"
        trigger="click"
        open
        classNames={{
          root: 'custom-popover-root',
          body: 'custom-popover-body',
        }}
        styles={{
          root: {
            backgroundColor: 'lightblue',
            border: '2px solid blue',
          },
          body: {
            padding: '20px',
            color: 'red',
          },
        }}
      >
        <span>Trigger</span>
      </Popover>,
    );

    const popover = document.querySelector('.ant-popover');
    const popoverBody = document.querySelector('.ant-popover-inner');

    expect(popover).toHaveClass('custom-popover-root');
    expect(popover).toHaveAttribute('style');
    const rootStyle = popover?.getAttribute('style');
    expect(rootStyle).toContain('background-color: lightblue');
    expect(rootStyle).toContain('border: 2px solid blue');

    expect(popoverBody).toHaveClass('custom-popover-body');
    expect(popoverBody).toHaveAttribute('style');
    const bodyStyle = popoverBody?.getAttribute('style');
    expect(bodyStyle).toContain('padding: 20px');
    expect(bodyStyle).toContain('color: red');
  });

  it('support classNames and styles as functions', () => {
    const { container } = render(
      <Popover
        title="Dynamic Title"
        content="Dynamic Content"
        trigger="hover"
        placement="top"
        open
        classNames={(info) => ({
          root: info.props.trigger === 'hover' ? 'hover-popover' : 'click-popover',
          body: `body-${info.props.placement}`,
        })}
        styles={(info) => ({
          root: {
            backgroundColor: info.props.trigger === 'hover' ? 'lightgreen' : 'lightcoral',
            borderRadius: info.props.placement === 'top' ? '8px' : '4px',
          },
          body: {
            fontSize: info.props.trigger === 'hover' ? '14px' : '12px',
            textAlign: info.props.placement === 'top' ? 'center' : 'left',
          },
        })}
      >
        <span>Dynamic Trigger</span>
      </Popover>,
    );

    const popover = document.querySelector('.ant-popover');
    const popoverBody = document.querySelector('.ant-popover-inner');

    expect(popover).toHaveClass('hover-popover');
    expect(popoverBody).toHaveClass('body-top');

    expect(popover).toHaveAttribute('style');
    const rootStyle = popover?.getAttribute('style');
    expect(rootStyle).toContain('background-color: lightgreen');
    expect(rootStyle).toContain('border-radius: 8px');

    expect(popoverBody).toHaveAttribute('style');
    const bodyStyle = popoverBody?.getAttribute('style');
    expect(bodyStyle).toContain('font-size: 14px');
    expect(bodyStyle).toContain('text-align: center');
  });
});
