// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/qr-code/demo/Popover.tsx extend context correctly 1`] = `
Array [
  <button
    aria-describedby="test-id"
    class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Hover me
    </span>
  </button>,
  <div
    class="ant-popover ant-zoom-big-appear ant-zoom-big-appear-prepare ant-zoom-big css-var-test-id css-var-test-id ant-popover-placement-top"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; right: auto; bottom: auto; box-sizing: border-box;"
  >
    <div
      class="ant-popover-arrow"
      style="position: absolute; bottom: 0px; left: 0px;"
    />
    <div
      class="ant-popover-content"
    >
      <div
        class="ant-popover-inner"
        id="test-id"
        role="tooltip"
      >
        <div
          class="ant-popover-inner-content"
        >
          <div
            class="ant-qrcode css-var-test-id ant-qrcode-borderless"
            style="background-color: transparent; width: 160px; height: 160px;"
          >
            <canvas
              height="160"
              role="img"
              width="160"
            />
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/qr-code/demo/Popover.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/base.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-qrcode css-var-test-id"
      style="background-color: transparent; width: 160px; height: 160px;"
    >
      <canvas
        height="160"
        role="img"
        width="160"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <input
      class="ant-input ant-input-outlined css-var-test-id ant-input-css-var"
      maxlength="60"
      placeholder="-"
      type="text"
      value="https://ant.design/"
    />
  </div>
</div>
`;

exports[`renders components/qr-code/demo/base.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/customColor.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-qrcode css-var-test-id"
      style="background-color: transparent; width: 160px; height: 160px;"
    >
      <canvas
        height="160"
        role="img"
        width="160"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-qrcode css-var-test-id"
      style="background-color: rgb(245, 245, 245); width: 160px; height: 160px;"
    >
      <canvas
        height="160"
        role="img"
        width="160"
      />
    </div>
  </div>
</div>
`;

exports[`renders components/qr-code/demo/customColor.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/customSize.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-space-compact"
    style="margin-bottom: 16px;"
  >
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-first-item"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="minus"
          class="anticon anticon-minus"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="minus"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M872 474H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h720c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"
            />
          </svg>
        </span>
      </span>
      <span>
        Smaller
      </span>
    </button>
    <button
      class="ant-btn css-var-test-id ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-compact-item ant-btn-compact-last-item"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="plus"
          class="anticon anticon-plus"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="plus"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
            />
            <path
              d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
            />
          </svg>
        </span>
      </span>
      <span>
        Larger
      </span>
    </button>
  </div>,
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <canvas
      height="160"
      role="img"
      width="160"
    />
    <img
      crossorigin="anonymous"
      src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
      style="display: none;"
    />
  </div>,
]
`;

exports[`renders components/qr-code/demo/customSize.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/customStatusRender.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-middle"
>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div
        class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
      >
        <div
          class="ant-space-item"
        >
          <div
            aria-busy="true"
            aria-live="polite"
            class="ant-spin ant-spin-spinning css-var-test-id"
          >
            <span
              class="ant-spin-dot-holder"
            >
              <span
                class="ant-spin-dot ant-spin-dot-spin"
              >
                <i
                  class="ant-spin-dot-item"
                />
                <i
                  class="ant-spin-dot-item"
                />
                <i
                  class="ant-spin-dot-item"
                />
                <i
                  class="ant-spin-dot-item"
                />
              </span>
            </span>
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <p>
            Loading...
          </p>
        </div>
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div>
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
          style="color: red;"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
         QR code expired
        <p>
          <button
            class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
            type="button"
          >
            <span
              aria-label="reload"
              class="anticon anticon-reload"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="reload"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"
                />
              </svg>
            </span>
            <span>
               Refresh
            </span>
          </button>
        </p>
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div>
        <span
          aria-label="check-circle"
          class="anticon anticon-check-circle"
          role="img"
          style="color: green;"
        >
          <svg
            aria-hidden="true"
            data-icon="check-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
            />
          </svg>
        </span>
         Scanned
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
</div>
`;

exports[`renders components/qr-code/demo/customStatusRender.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/download.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
  id="myqrcode"
>
  <div
    class="ant-space-item"
  >
    <div
      aria-label="segmented control"
      class="ant-segmented css-var-test-id"
      role="radiogroup"
      tabindex="0"
    >
      <div
        class="ant-segmented-group"
      >
        <label
          class="ant-segmented-item ant-segmented-item-selected"
        >
          <input
            checked=""
            class="ant-segmented-item-input"
            name="test-id"
            type="radio"
          />
          <div
            aria-checked="true"
            class="ant-segmented-item-label"
            role="radio"
            title="canvas"
          >
            canvas
          </div>
        </label>
        <label
          class="ant-segmented-item"
        >
          <input
            class="ant-segmented-item-input"
            name="test-id"
            type="radio"
          />
          <div
            aria-checked="false"
            class="ant-segmented-item-label"
            role="radio"
            title="svg"
          >
            svg
          </div>
        </label>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <div
        class="ant-qrcode css-var-test-id"
        style="background-color: rgb(255, 255, 255); margin-bottom: 16px; width: 160px; height: 160px;"
      >
        <canvas
          height="160"
          role="img"
          width="160"
        />
        <img
          crossorigin="anonymous"
          src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
          style="display: none;"
        />
      </div>
      <button
        class="ant-btn css-var-test-id ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
        type="button"
      >
        <span>
          Download
        </span>
      </button>
    </div>
  </div>
</div>
`;

exports[`renders components/qr-code/demo/download.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/errorlevel.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; margin-bottom: 16px; width: 160px; height: 160px;"
  >
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>,
  <div
    aria-label="segmented control"
    class="ant-segmented css-var-test-id"
    role="radiogroup"
    tabindex="0"
  >
    <div
      class="ant-segmented-group"
    >
      <label
        class="ant-segmented-item ant-segmented-item-selected"
      >
        <input
          checked=""
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="true"
          class="ant-segmented-item-label"
          role="radio"
          title="L"
        >
          L
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="M"
        >
          M
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="Q"
        >
          Q
        </div>
      </label>
      <label
        class="ant-segmented-item"
      >
        <input
          class="ant-segmented-item-input"
          name="test-id"
          type="radio"
        />
        <div
          aria-checked="false"
          class="ant-segmented-item-label"
          role="radio"
          title="H"
        >
          H
        </div>
      </label>
    </div>
  </div>,
]
`;

exports[`renders components/qr-code/demo/errorlevel.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/icon.tsx extend context correctly 1`] = `
<div
  class="ant-qrcode css-var-test-id"
  style="background-color: transparent; width: 160px; height: 160px;"
>
  <canvas
    height="160"
    role="img"
    width="160"
  />
  <img
    crossorigin="anonymous"
    src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
    style="display: none;"
  />
</div>
`;

exports[`renders components/qr-code/demo/icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/status.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-middle"
>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <div
        aria-busy="true"
        aria-live="polite"
        class="ant-spin ant-spin-spinning css-var-test-id"
      >
        <span
          class="ant-spin-dot-holder"
        >
          <span
            class="ant-spin-dot ant-spin-dot-spin"
          >
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
            <i
              class="ant-spin-dot-item"
            />
          </span>
        </span>
      </div>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <p
        class="ant-qrcode-expired"
      >
        QR code expired
      </p>
      <button
        class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
        type="button"
      >
        <span
          class="ant-btn-icon"
        >
          <span
            aria-label="reload"
            class="anticon anticon-reload"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="reload"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"
              />
            </svg>
          </span>
        </span>
        <span>
          Refresh
        </span>
      </button>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
  <div
    class="ant-qrcode css-var-test-id"
    style="background-color: transparent; width: 160px; height: 160px;"
  >
    <div
      class="ant-qrcode-cover"
    >
      <p
        class="ant-qrcode-scanned"
      >
        Scanned
      </p>
    </div>
    <canvas
      height="160"
      role="img"
      width="160"
    />
  </div>
</div>
`;

exports[`renders components/qr-code/demo/status.tsx extend context correctly 2`] = `[]`;

exports[`renders components/qr-code/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-large ant-space-gap-col-large css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        对象形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id custom-qrcode-root"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 8px; padding: 16px; width: 160px; height: 160px;"
          >
            <canvas
              height="160"
              role="img"
              width="160"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id custom-qrcode-with-icon"
            style="background-color: rgb(246, 255, 237); border: 2px solid rgb(82, 196, 26); border-radius: 12px; padding: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); width: 160px; height: 160px;"
          >
            <canvas
              height="160"
              role="img"
              width="160"
            />
            <img
              crossorigin="anonymous"
              src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
              style="display: none;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        函数形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id canvas-qrcode"
            style="background-color: rgba(255, 77, 79, 0.1); border: 2px solid rgb(255, 77, 79); border-radius: 8px; padding: 8px; transform: scale(1); width: 120px; height: 120px;"
          >
            <canvas
              height="120"
              role="img"
              width="120"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id dynamic-svg-140 ant-qrcode-borderless"
            style="background-color: rgba(114, 46, 209, 0.1); border-radius: 16px; padding: 16px; box-shadow: none; width: 140px; height: 140px;"
          >
            <svg
              height="140"
              role="img"
              viewBox="0 0 25 25"
              width="140"
            >
              <path
                d="M0,0 h25v25H0z"
                fill="transparent"
                shape-rendering="crispEdges"
              />
              <path
                d="M0 0h7v1H0zM8 0h2v1H8zM11 0h5v1H11zM18,0 h7v1H18zM0 1h1v1H0zM6 1h1v1H6zM10 1h6v1H10zM18 1h1v1H18zM24,1 h1v1H24zM0 2h1v1H0zM2 2h3v1H2zM6 2h1v1H6zM8 2h1v1H8zM10 2h1v1H10zM12 2h1v1H12zM15 2h1v1H15zM18 2h1v1H18zM20 2h3v1H20zM24,2 h1v1H24zM0 3h1v1H0zM2 3h3v1H2zM6 3h1v1H6zM8 3h2v1H8zM15 3h1v1H15zM18 3h1v1H18zM20 3h3v1H20zM24,3 h1v1H24zM0 4h1v1H0zM2 4h3v1H2zM6 4h1v1H6zM10 4h1v1H10zM12 4h1v1H12zM14 4h3v1H14zM18 4h1v1H18zM20 4h3v1H20zM24,4 h1v1H24zM0 5h1v1H0zM6 5h1v1H6zM8 5h1v1H8zM11 5h1v1H11zM15 5h2v1H15zM18 5h1v1H18zM24,5 h1v1H24zM0 6h7v1H0zM8 6h1v1H8zM10 6h1v1H10zM12 6h1v1H12zM14 6h1v1H14zM16 6h1v1H16zM18,6 h7v1H18zM8 7h1v1H8zM14 7h3v1H14zM1 8h1v1H1zM3 8h1v1H3zM5 8h8v1H5zM15 8h5v1H15zM21 8h2v1H21zM24,8 h1v1H24zM0 9h3v1H0zM7 9h1v1H7zM9 9h2v1H9zM16 9h3v1H16zM24,9 h1v1H24zM4 10h1v1H4zM6 10h2v1H6zM16 10h2v1H16zM20 10h1v1H20zM23,10 h2v1H23zM0 11h1v1H0zM2 11h2v1H2zM8 11h1v1H8zM11 11h2v1H11zM14 11h1v1H14zM18 11h3v1H18zM0 12h2v1H0zM4 12h4v1H4zM10 12h3v1H10zM17 12h2v1H17zM21 12h1v1H21zM23,12 h2v1H23zM1 13h2v1H1zM5 13h1v1H5zM7 13h1v1H7zM9 13h1v1H9zM11 13h1v1H11zM18 13h2v1H18zM21 13h2v1H21zM24,13 h1v1H24zM0 14h1v1H0zM2 14h2v1H2zM6 14h2v1H6zM9 14h1v1H9zM11 14h1v1H11zM18 14h3v1H18zM22 14h1v1H22zM24,14 h1v1H24zM1 15h1v1H1zM3 15h3v1H3zM7 15h1v1H7zM9 15h2v1H9zM13 15h1v1H13zM15 15h3v1H15zM20 15h1v1H20zM23 15h1v1H23zM0 16h3v1H0zM4 16h1v1H4zM6 16h1v1H6zM9 16h1v1H9zM12 16h1v1H12zM14 16h1v1H14zM16 16h7v1H16zM8 17h1v1H8zM10 17h1v1H10zM12 17h5v1H12zM20 17h2v1H20zM24,17 h1v1H24zM0 18h7v1H0zM8 18h3v1H8zM14 18h3v1H14zM18 18h1v1H18zM20 18h2v1H20zM23,18 h2v1H23zM0 19h1v1H0zM6 19h1v1H6zM8 19h2v1H8zM13 19h1v1H13zM16 19h1v1H16zM20 19h3v1H20zM24,19 h1v1H24zM0 20h1v1H0zM2 20h3v1H2zM6 20h1v1H6zM9 20h1v1H9zM11 20h3v1H11zM16 20h6v1H16zM23,20 h2v1H23zM0 21h1v1H0zM2 21h3v1H2zM6 21h1v1H6zM8 21h2v1H8zM11 21h1v1H11zM15 21h1v1H15zM17 21h1v1H17zM19 21h4v1H19zM0 22h1v1H0zM2 22h3v1H2zM6 22h1v1H6zM9 22h5v1H9zM15 22h1v1H15zM19 22h2v1H19zM22 22h1v1H22zM24,22 h1v1H24zM0 23h1v1H0zM6 23h1v1H6zM8 23h1v1H8zM10 23h1v1H10zM13 23h1v1H13zM15 23h4v1H15zM21 23h1v1H21zM0 24h7v1H0zM14 24h3v1H14zM19 24h1v1H19zM23,24 h2v1H23z"
                fill="rgba(0,0,0,0.88)"
                shape-rendering="crispEdges"
              />
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        不同状态的 QRCode 样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id status-active"
            style="background-color: rgba(82, 196, 26, 0.063); border: 2px solid rgb(82, 196, 26); border-radius: 8px; padding: 12px; opacity: 1; width: 120px; height: 120px;"
          >
            <canvas
              height="120"
              role="img"
              width="120"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id status-expired"
            style="background-color: rgba(255, 77, 79, 0.063); border: 2px solid rgb(255, 77, 79); border-radius: 8px; padding: 12px; opacity: 0.8; width: 120px; height: 120px;"
          >
            <div
              class="ant-qrcode-cover cover-expired"
              style="background-color: rgba(255, 77, 79, 0.8); color: rgb(255, 255, 255); border-radius: 6px; text-align: center;"
            >
              <p
                class="ant-qrcode-expired"
              >
                QR code expired
              </p>
              <button
                class="ant-btn css-var-test-id ant-btn-link ant-btn-color-link ant-btn-variant-link"
                type="button"
              >
                <span
                  class="ant-btn-icon"
                >
                  <span
                    aria-label="reload"
                    class="anticon anticon-reload"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="reload"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"
                      />
                    </svg>
                  </span>
                </span>
                <span>
                  Refresh
                </span>
              </button>
            </div>
            <canvas
              height="120"
              role="img"
              width="120"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id status-loading"
            style="background-color: rgba(24, 144, 255, 0.063); border: 2px solid rgb(24, 144, 255); border-radius: 8px; padding: 12px; opacity: 0.8; width: 120px; height: 120px;"
          >
            <div
              class="ant-qrcode-cover cover-loading"
              style="background-color: rgba(24, 144, 255, 0.8); color: rgb(255, 255, 255); border-radius: 6px; text-align: center;"
            >
              <div
                aria-busy="true"
                aria-live="polite"
                class="ant-spin ant-spin-spinning css-var-test-id"
              >
                <span
                  class="ant-spin-dot-holder"
                >
                  <span
                    class="ant-spin-dot ant-spin-dot-spin"
                  >
                    <i
                      class="ant-spin-dot-item"
                    />
                    <i
                      class="ant-spin-dot-item"
                    />
                    <i
                      class="ant-spin-dot-item"
                    />
                    <i
                      class="ant-spin-dot-item"
                    />
                  </span>
                </span>
              </div>
            </div>
            <canvas
              height="120"
              role="img"
              width="120"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        不同尺寸的 QRCode 样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id size-80"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 4px; padding: 8px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); width: 80px; height: 80px;"
          >
            <canvas
              height="80"
              role="img"
              width="80"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id size-120"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 8px; padding: 12px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); width: 120px; height: 120px;"
          >
            <canvas
              height="120"
              role="img"
              width="120"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id size-160"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 12px; padding: 16px; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); width: 160px; height: 160px;"
          >
            <canvas
              height="160"
              role="img"
              width="160"
            />
          </div>
        </div>
        <div
          class="ant-space-item"
        >
          <div
            class="ant-qrcode css-var-test-id size-200"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 12px; padding: 16px; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); width: 200px; height: 200px;"
          >
            <canvas
              height="200"
              role="img"
              width="200"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/qr-code/demo/style-class.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Space] \`direction\` is deprecated. Please use \`orientation\` instead.",
]
`;

exports[`renders components/qr-code/demo/type.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-qrcode css-var-test-id"
      style="background-color: transparent; width: 160px; height: 160px;"
    >
      <canvas
        height="160"
        role="img"
        width="160"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-qrcode css-var-test-id"
      style="background-color: transparent; width: 160px; height: 160px;"
    >
      <svg
        height="160"
        role="img"
        viewBox="0 0 25 25"
        width="160"
      >
        <path
          d="M0,0 h25v25H0z"
          fill="transparent"
          shape-rendering="crispEdges"
        />
        <path
          d="M0 0h7v1H0zM8 0h2v1H8zM11 0h5v1H11zM18,0 h7v1H18zM0 1h1v1H0zM6 1h1v1H6zM10 1h6v1H10zM18 1h1v1H18zM24,1 h1v1H24zM0 2h1v1H0zM2 2h3v1H2zM6 2h1v1H6zM8 2h1v1H8zM10 2h1v1H10zM12 2h1v1H12zM15 2h1v1H15zM18 2h1v1H18zM20 2h3v1H20zM24,2 h1v1H24zM0 3h1v1H0zM2 3h3v1H2zM6 3h1v1H6zM8 3h2v1H8zM15 3h1v1H15zM18 3h1v1H18zM20 3h3v1H20zM24,3 h1v1H24zM0 4h1v1H0zM2 4h3v1H2zM6 4h1v1H6zM10 4h1v1H10zM12 4h1v1H12zM14 4h3v1H14zM18 4h1v1H18zM20 4h3v1H20zM24,4 h1v1H24zM0 5h1v1H0zM6 5h1v1H6zM8 5h1v1H8zM11 5h1v1H11zM15 5h2v1H15zM18 5h1v1H18zM24,5 h1v1H24zM0 6h7v1H0zM8 6h1v1H8zM10 6h1v1H10zM12 6h1v1H12zM14 6h1v1H14zM16 6h1v1H16zM18,6 h7v1H18zM8 7h1v1H8zM14 7h3v1H14zM1 8h1v1H1zM3 8h1v1H3zM5 8h8v1H5zM15 8h5v1H15zM21 8h2v1H21zM24,8 h1v1H24zM0 9h3v1H0zM7 9h1v1H7zM9 9h2v1H9zM16 9h3v1H16zM24,9 h1v1H24zM4 10h1v1H4zM6 10h2v1H6zM16 10h2v1H16zM20 10h1v1H20zM23,10 h2v1H23zM0 11h1v1H0zM2 11h2v1H2zM8 11h1v1H8zM11 11h2v1H11zM14 11h1v1H14zM18 11h3v1H18zM0 12h2v1H0zM4 12h4v1H4zM10 12h3v1H10zM17 12h2v1H17zM21 12h1v1H21zM23,12 h2v1H23zM1 13h2v1H1zM5 13h1v1H5zM7 13h1v1H7zM9 13h1v1H9zM11 13h1v1H11zM18 13h2v1H18zM21 13h2v1H21zM24,13 h1v1H24zM0 14h1v1H0zM2 14h2v1H2zM6 14h2v1H6zM9 14h1v1H9zM11 14h1v1H11zM18 14h3v1H18zM22 14h1v1H22zM24,14 h1v1H24zM1 15h1v1H1zM3 15h3v1H3zM7 15h1v1H7zM9 15h2v1H9zM13 15h1v1H13zM15 15h3v1H15zM20 15h1v1H20zM23 15h1v1H23zM0 16h3v1H0zM4 16h1v1H4zM6 16h1v1H6zM9 16h1v1H9zM12 16h1v1H12zM14 16h1v1H14zM16 16h7v1H16zM8 17h1v1H8zM10 17h1v1H10zM12 17h5v1H12zM20 17h2v1H20zM24,17 h1v1H24zM0 18h7v1H0zM8 18h3v1H8zM14 18h3v1H14zM18 18h1v1H18zM20 18h2v1H20zM23,18 h2v1H23zM0 19h1v1H0zM6 19h1v1H6zM8 19h2v1H8zM13 19h1v1H13zM16 19h1v1H16zM20 19h3v1H20zM24,19 h1v1H24zM0 20h1v1H0zM2 20h3v1H2zM6 20h1v1H6zM9 20h1v1H9zM11 20h3v1H11zM16 20h6v1H16zM23,20 h2v1H23zM0 21h1v1H0zM2 21h3v1H2zM6 21h1v1H6zM8 21h2v1H8zM11 21h1v1H11zM15 21h1v1H15zM17 21h1v1H17zM19 21h4v1H19zM0 22h1v1H0zM2 22h3v1H2zM6 22h1v1H6zM9 22h5v1H9zM15 22h1v1H15zM19 22h2v1H19zM22 22h1v1H22zM24,22 h1v1H24zM0 23h1v1H0zM6 23h1v1H6zM8 23h1v1H8zM10 23h1v1H10zM13 23h1v1H13zM15 23h4v1H15zM21 23h1v1H21zM0 24h7v1H0zM14 24h3v1H14zM19 24h1v1H19zM23,24 h2v1H23z"
          fill="rgba(0,0,0,0.88)"
          shape-rendering="crispEdges"
        />
      </svg>
    </div>
  </div>
</div>
`;

exports[`renders components/qr-code/demo/type.tsx extend context correctly 2`] = `[]`;
