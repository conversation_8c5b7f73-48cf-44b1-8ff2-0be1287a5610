---
order: 25
title:
  en-US: Custom Styles and ClassNames
  zh-CN: 自定义样式和类名
debug: true
---

## zh-CN

使用 `classNames` 和 `styles` 属性来自定义表格的各个语义化 DOM 结构的样式。

- `classNames` 和 `styles` 支持传入对象，为表格的不同部分设置样式
- `classNames` 和 `styles` 支持传入函数，可以根据组件的 props 动态设置样式

## en-US

Use `classNames` and `styles` properties to customize styles for semantic DOM structures of the table.

- `classNames` and `styles` support passing objects to set styles for different parts of the table
- `classNames` and `styles` support passing functions to dynamically set styles based on component props
