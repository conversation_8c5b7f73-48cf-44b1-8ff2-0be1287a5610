// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/tag/demo/animation.tsx extend context correctly 1`] = `
Array [
  <div
    style="display: flex; align-items: center; flex-wrap: wrap; gap: 8px; margin-bottom: 8px;"
  >
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
    >
      Tag 1
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
    >
      Tag 2
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
    >
      Tag 3
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
    style="background: rgb(255, 255, 255); border-style: dashed;"
  >
    <span
      aria-label="plus"
      class="anticon anticon-plus"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="plus"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
        />
        <path
          d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
        />
      </svg>
    </span>
     New Tag
  </span>,
]
`;

exports[`renders components/tag/demo/animation.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/basic.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Tag 1
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    <a
      href="https://github.com/ant-design/ant-design/issues/1862"
      rel="noopener noreferrer"
      target="_blank"
    >
      Link
    </a>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Prevent Default
    <span
      aria-label="Close"
      class="anticon anticon-close ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="close"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Tag 2
    <span
      aria-label="Close"
      class="anticon anticon-close-circle ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Tag 3
    <span
      aria-label="Close Button"
      class="anticon anticon-delete ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="delete"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/tag/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/checkable.tsx extend context correctly 1`] = `
<form
  class="ant-form ant-form-horizontal css-var-test-id ant-form-css-var"
>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label css-var-test-id"
      >
        <label
          class=""
          title="Checkable"
        >
          Checkable
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-tag ant-tag-checkable ant-tag-checkable-checked css-var-test-id"
            >
              <span>
                Yes
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label css-var-test-id"
      >
        <label
          class=""
          title="Single"
        >
          Single
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-tag-checkable-group css-var-test-id"
            >
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Movies
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-checked ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Books
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Music
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Sports
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item css-var-test-id ant-form-css-var ant-form-item-horizontal"
  >
    <div
      class="ant-row ant-form-item-row css-var-test-id"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label css-var-test-id"
      >
        <label
          class=""
          title="Multiple"
        >
          Multiple
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control css-var-test-id"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-tag-checkable-group css-var-test-id"
            >
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-checked ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Movies
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Books
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-checked ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Music
                </span>
              </span>
              <span
                class="ant-tag ant-tag-checkable ant-tag-checkable-group-item css-var-test-id"
              >
                <span>
                  Sports
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/tag/demo/checkable.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/colorful.tsx extend context correctly 1`] = `
Array [
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Presets (filled)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-filled ant-tag-magenta css-var-test-id"
      >
        magenta
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-red css-var-test-id"
      >
        red
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-volcano css-var-test-id"
      >
        volcano
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-orange css-var-test-id"
      >
        orange
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-gold css-var-test-id"
      >
        gold
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-lime css-var-test-id"
      >
        lime
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-green css-var-test-id"
      >
        green
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-cyan css-var-test-id"
      >
        cyan
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-blue css-var-test-id"
      >
        blue
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-geekblue css-var-test-id"
      >
        geekblue
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-purple css-var-test-id"
      >
        purple
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Presets (solid)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-solid ant-tag-magenta css-var-test-id"
      >
        magenta
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-red css-var-test-id"
      >
        red
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-volcano css-var-test-id"
      >
        volcano
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-orange css-var-test-id"
      >
        orange
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-gold css-var-test-id"
      >
        gold
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-lime css-var-test-id"
      >
        lime
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-green css-var-test-id"
      >
        green
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-cyan css-var-test-id"
      >
        cyan
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-blue css-var-test-id"
      >
        blue
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-geekblue css-var-test-id"
      >
        geekblue
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-purple css-var-test-id"
      >
        purple
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Presets (outlined)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-outlined ant-tag-magenta css-var-test-id"
      >
        magenta
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-red css-var-test-id"
      >
        red
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-volcano css-var-test-id"
      >
        volcano
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-orange css-var-test-id"
      >
        orange
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-gold css-var-test-id"
      >
        gold
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-lime css-var-test-id"
      >
        lime
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-green css-var-test-id"
      >
        green
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-cyan css-var-test-id"
      >
        cyan
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-blue css-var-test-id"
      >
        blue
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-geekblue css-var-test-id"
      >
        geekblue
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-purple css-var-test-id"
      >
        purple
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Custom (filled)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-filled css-var-test-id"
        style="background-color: rgb(255, 238, 229); color: rgb(255, 85, 0);"
      >
        #f50
      </span>
      <span
        class="ant-tag ant-tag-filled css-var-test-id"
        style="background-color: rgb(232, 246, 253); color: rgb(45, 183, 245);"
      >
        #2db7f5
      </span>
      <span
        class="ant-tag ant-tag-filled css-var-test-id"
        style="background-color: rgb(240, 249, 236); color: rgb(135, 208, 104);"
      >
        #87d068
      </span>
      <span
        class="ant-tag ant-tag-filled css-var-test-id"
        style="background-color: rgb(230, 244, 254); color: rgb(16, 142, 233);"
      >
        #108ee9
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Custom (solid)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-solid css-var-test-id"
        style="background-color: rgb(255, 85, 0);"
      >
        #f50
      </span>
      <span
        class="ant-tag ant-tag-solid css-var-test-id"
        style="background-color: rgb(45, 183, 245);"
      >
        #2db7f5
      </span>
      <span
        class="ant-tag ant-tag-solid css-var-test-id"
        style="background-color: rgb(135, 208, 104);"
      >
        #87d068
      </span>
      <span
        class="ant-tag ant-tag-solid css-var-test-id"
        style="background-color: rgb(16, 142, 233);"
      >
        #108ee9
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Custom (outlined)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-outlined css-var-test-id"
        style="background-color: rgb(255, 238, 229); color: rgb(255, 85, 0); border-color: rgb(255, 85, 0);"
      >
        #f50
      </span>
      <span
        class="ant-tag ant-tag-outlined css-var-test-id"
        style="background-color: rgb(232, 246, 253); color: rgb(45, 183, 245); border-color: rgb(45, 183, 245);"
      >
        #2db7f5
      </span>
      <span
        class="ant-tag ant-tag-outlined css-var-test-id"
        style="background-color: rgb(240, 249, 236); color: rgb(135, 208, 104); border-color: rgb(135, 208, 104);"
      >
        #87d068
      </span>
      <span
        class="ant-tag ant-tag-outlined css-var-test-id"
        style="background-color: rgb(230, 244, 254); color: rgb(16, 142, 233); border-color: rgb(16, 142, 233);"
      >
        #108ee9
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/tag/demo/colorful.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/control.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
    style="user-select: none;"
  >
    <span>
      Unremovable
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
    style="user-select: none;"
  >
    <span>
      Tag 2
    </span>
    <span
      aria-label="Close"
      class="anticon anticon-close ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="close"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
    style="user-select: none;"
  >
    <span>
      Tag 3
    </span>
    <span
      aria-label="Close"
      class="anticon anticon-close ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="close"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
    style="height: 22px; background: rgb(255, 255, 255); border-style: dashed;"
  >
    <span
      aria-label="plus"
      class="anticon anticon-plus"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="plus"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
        />
        <path
          d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
        />
      </svg>
    </span>
    <span>
      New Tag
    </span>
  </span>
</div>
`;

exports[`renders components/tag/demo/control.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/customize.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Tag1
    <span
      aria-label="Close"
      class="ant-tag-close-icon"
    >
      关 闭
    </span>
  </span>
  <span
    class="ant-tag ant-tag-filled css-var-test-id"
  >
    Tag2
    <span
      aria-label="Close"
      class="anticon anticon-close-circle ant-tag-close-icon"
      role="img"
      tabindex="-1"
    >
      <svg
        aria-hidden="true"
        data-icon="close-circle"
        fill="currentColor"
        fill-rule="evenodd"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`renders components/tag/demo/customize.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/disabled.tsx extend context correctly 1`] = `
<div
  class="ant-flex css-var-test-id ant-flex-align-stretch ant-flex-gap-middle ant-flex-vertical"
>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Basic Tag
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      <a
        href="https://ant.design"
      >
        Link Tag
      </a>
    </span>
    <a
      aria-disabled="true"
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Href Tag
    </a>
    <span
      class="ant-tag ant-tag-filled ant-tag-success ant-tag-disabled css-var-test-id"
    >
      <span
        aria-label="check-circle"
        class="anticon anticon-check-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="check-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
          />
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
        </svg>
      </span>
      <span>
        Icon Tag
      </span>
    </span>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <span
      class="ant-tag ant-tag-filled ant-tag-red ant-tag-disabled css-var-test-id"
    >
      Preset Color Red
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Custom Color #f50 Outlined
    </span>
    <span
      class="ant-tag ant-tag-solid ant-tag-disabled css-var-test-id"
    >
      Custom Color #f50 Filled
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Custom Color #f50 Borderless
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-success ant-tag-disabled css-var-test-id"
    >
      Preset Status Success
    </span>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <span
      class="ant-tag ant-tag-checkable ant-tag-checkable-checked ant-tag-checkable-disabled css-var-test-id"
    >
      <span>
        Books
      </span>
    </span>
    <span
      class="ant-tag ant-tag-checkable ant-tag-checkable-disabled css-var-test-id"
    >
      <span>
        Movies
      </span>
    </span>
    <span
      class="ant-tag ant-tag-checkable ant-tag-checkable-disabled css-var-test-id"
    >
      <span>
        Music
      </span>
    </span>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Closable Tag
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-success ant-tag-disabled css-var-test-id"
    >
      <span
        aria-label="check-circle"
        class="anticon anticon-check-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="check-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
          />
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
        </svg>
      </span>
      <span>
        Closable with Icon
      </span>
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Closable with Custom Icon
      <span
        aria-label="Close"
        class="anticon anticon-close-circle ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-gap-small"
  >
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Borderless Basic
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-success ant-tag-disabled css-var-test-id"
    >
      <span
        aria-label="check-circle"
        class="anticon anticon-check-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="check-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
          />
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
        </svg>
      </span>
      <span>
        Borderless with Icon
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled ant-tag-disabled css-var-test-id"
    >
      Borderless Closable
      <span
        aria-label="Close"
        class="anticon anticon-close ant-tag-close-icon"
        role="img"
        tabindex="-1"
      >
        <svg
          aria-hidden="true"
          data-icon="close"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/tag/demo/disabled.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/draggable.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-flex ant-flex-wrap-wrap"
    style="gap: 4px 0;"
  >
    <span
      class="ant-tag"
      style="cursor: move; transition: unset;"
    >
      Tag 1
    </span>
    <span
      class="ant-tag"
      style="cursor: move; transition: unset;"
    >
      Tag 2
    </span>
    <span
      class="ant-tag"
      style="cursor: move; transition: unset;"
    >
      Tag 3
    </span>
  </div>,
  <div
    id="DndDescribedBy-1"
    style="display: none;"
  >
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  </div>,
  <div
    aria-atomic="true"
    aria-live="assertive"
    id="DndLiveRegion-1"
    role="status"
    style="position: fixed; top: 0px; left: 0px; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip-path: inset(100%); white-space: nowrap;"
  />,
]
`;

exports[`renders components/tag/demo/draggable.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/icon.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      Tag with icon
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center"
    style="gap: 4px 0;"
  >
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
      style="background-color: rgb(234, 243, 250); color: rgb(85, 172, 238);"
    >
      <span
        aria-label="twitter"
        class="anticon anticon-twitter"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="twitter"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 254.3c-30.6 13.2-63.9 22.7-98.2 26.4a170.1 170.1 0 0075-94 336.64 336.64 0 01-108.2 41.2A170.1 170.1 0 00672 174c-94.5 0-170.5 76.6-170.5 170.6 0 13.2 1.6 26.4 4.2 39.1-141.5-7.4-267.7-75-351.6-178.5a169.32 169.32 0 00-23.2 86.1c0 59.2 30.1 111.4 76 142.1a172 172 0 01-77.1-21.7v2.1c0 82.9 58.6 151.6 136.7 167.4a180.6 180.6 0 01-44.9 5.8c-11.1 0-21.6-1.1-32.2-2.6C211 652 273.9 701.1 348.8 702.7c-58.6 45.9-132 72.9-211.7 72.9-14.3 0-27.5-.5-41.2-2.1C171.5 822 261.2 850 357.8 850 671.4 850 843 590.2 843 364.7c0-7.4 0-14.8-.5-22.2 33.2-24.3 62.3-54.4 85.5-88.2z"
          />
        </svg>
      </span>
      <span>
        Twitter
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
      style="background-color: rgb(253, 231, 231); color: rgb(205, 32, 31);"
    >
      <span
        aria-label="youtube"
        class="anticon anticon-youtube"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="youtube"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M960 509.2c0-2.2 0-4.7-.1-7.6-.1-8.1-.3-17.2-.5-26.9-.8-27.9-2.2-55.7-4.4-81.9-3-36.1-7.4-66.2-13.4-88.8a139.52 139.52 0 00-98.3-98.5c-28.3-7.6-83.7-12.3-161.7-15.2-37.1-1.4-76.8-2.3-116.5-2.8-13.9-.2-26.8-.3-38.4-.4h-29.4c-11.6.1-24.5.2-38.4.4-39.7.5-79.4 1.4-116.5 2.8-78 3-133.5 7.7-161.7 15.2A139.35 139.35 0 0082.4 304C76.3 326.6 72 356.7 69 392.8c-2.2 26.2-3.6 54-4.4 81.9-.3 9.7-.4 18.8-.5 26.9 0 2.9-.1 5.4-.1 7.6v5.6c0 2.2 0 4.7.1 7.6.1 8.1.3 17.2.5 26.9.8 27.9 2.2 55.7 4.4 81.9 3 36.1 7.4 66.2 13.4 88.8 12.8 47.9 50.4 85.7 98.3 98.5 28.2 7.6 83.7 12.3 161.7 15.2 37.1 1.4 76.8 2.3 116.5 2.8 13.9.2 26.8.3 38.4.4h29.4c11.6-.1 24.5-.2 38.4-.4 39.7-.5 79.4-1.4 116.5-2.8 78-3 133.5-7.7 161.7-15.2 47.9-12.8 85.5-50.5 98.3-98.5 6.1-22.6 10.4-52.7 13.4-88.8 2.2-26.2 3.6-54 4.4-81.9.3-9.7.4-18.8.5-26.9 0-2.9.1-5.4.1-7.6v-5.6zm-72 5.2c0 2.1 0 4.4-.1 7.1-.1 7.8-.3 16.4-.5 25.7-.7 26.6-2.1 53.2-4.2 77.9-2.7 32.2-6.5 58.6-11.2 76.3-6.2 23.1-24.4 41.4-47.4 47.5-21 5.6-73.9 10.1-145.8 12.8-36.4 1.4-75.6 2.3-114.7 2.8-13.7.2-26.4.3-37.8.3h-28.6l-37.8-.3c-39.1-.5-78.2-1.4-114.7-2.8-71.9-2.8-124.9-7.2-145.8-12.8-23-6.2-41.2-24.4-47.4-47.5-4.7-17.7-8.5-44.1-11.2-76.3-2.1-24.7-3.4-51.3-4.2-77.9-.3-9.3-.4-18-.5-25.7 0-2.7-.1-5.1-.1-7.1v-4.8c0-2.1 0-4.4.1-7.1.1-7.8.3-16.4.5-25.7.7-26.6 2.1-53.2 4.2-77.9 2.7-32.2 6.5-58.6 11.2-76.3 6.2-23.1 24.4-41.4 47.4-47.5 21-5.6 73.9-10.1 145.8-12.8 36.4-1.4 75.6-2.3 114.7-2.8 13.7-.2 26.4-.3 37.8-.3h28.6l37.8.3c39.1.5 78.2 1.4 114.7 2.8 71.9 2.8 124.9 7.2 145.8 12.8 23 6.2 41.2 24.4 47.4 47.5 4.7 17.7 8.5 44.1 11.2 76.3 2.1 24.7 3.4 51.3 4.2 77.9.3 9.3.4 18 .5 25.7 0 2.7.1 5.1.1 7.1v4.8zM423 646l232-135-232-133z"
          />
        </svg>
      </span>
      <span>
        Youtube
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
      style="background-color: rgb(234, 239, 250); color: rgb(59, 89, 153);"
    >
      <span
        aria-label="facebook"
        class="anticon anticon-facebook"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="facebook"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-32 736H663.9V602.2h104l15.6-120.7H663.9v-77.1c0-35 9.7-58.8 59.8-58.8h63.9v-108c-11.1-1.5-49-4.8-93.2-4.8-92.2 0-155.3 56.3-155.3 159.6v89H434.9v120.7h104.3V848H176V176h672v672z"
          />
        </svg>
      </span>
      <span>
        Facebook
      </span>
    </span>
    <span
      class="ant-tag ant-tag-filled css-var-test-id"
      style="background-color: rgb(234, 243, 250); color: rgb(85, 172, 238);"
    >
      <span
        aria-label="linkedin"
        class="anticon anticon-linkedin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="linkedin"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M847.7 112H176.3c-35.5 0-64.3 28.8-64.3 64.3v671.4c0 35.5 28.8 64.3 64.3 64.3h671.4c35.5 0 64.3-28.8 64.3-64.3V176.3c0-35.5-28.8-64.3-64.3-64.3zm0 736c-447.8-.1-671.7-.2-671.7-.3.1-447.8.2-671.7.3-671.7 447.8.1 671.7.2 671.7.3-.1 447.8-.2 671.7-.3 671.7zM230.6 411.9h118.7v381.8H230.6zm59.4-52.2c37.9 0 68.8-30.8 68.8-68.8a68.8 68.8 0 10-137.6 0c-.1 38 30.7 68.8 68.8 68.8zm252.3 245.1c0-49.8 9.5-98 71.2-98 60.8 0 61.7 56.9 61.7 101.2v185.7h118.6V584.3c0-102.8-22.2-181.9-142.3-181.9-57.7 0-96.4 31.7-112.3 61.7h-1.6v-52.2H423.7v381.8h118.6V604.8z"
          />
        </svg>
      </span>
      <span>
        LinkedIn
      </span>
    </span>
  </div>,
  <div
    class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
    role="separator"
  >
    <div
      class="ant-divider-rail ant-divider-rail-start"
    />
    <span
      class="ant-divider-inner-text"
    >
      CheckableTag with icon
    </span>
    <div
      class="ant-divider-rail ant-divider-rail-end"
    />
  </div>,
  <div
    class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center"
    style="gap: 4px 0;"
  >
    <span
      class="ant-tag ant-tag-checkable ant-tag-checkable-checked css-var-test-id"
    >
      <span
        aria-label="twitter"
        class="anticon anticon-twitter"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="twitter"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M928 254.3c-30.6 13.2-63.9 22.7-98.2 26.4a170.1 170.1 0 0075-94 336.64 336.64 0 01-108.2 41.2A170.1 170.1 0 00672 174c-94.5 0-170.5 76.6-170.5 170.6 0 13.2 1.6 26.4 4.2 39.1-141.5-7.4-267.7-75-351.6-178.5a169.32 169.32 0 00-23.2 86.1c0 59.2 30.1 111.4 76 142.1a172 172 0 01-77.1-21.7v2.1c0 82.9 58.6 151.6 136.7 167.4a180.6 180.6 0 01-44.9 5.8c-11.1 0-21.6-1.1-32.2-2.6C211 652 273.9 701.1 348.8 702.7c-58.6 45.9-132 72.9-211.7 72.9-14.3 0-27.5-.5-41.2-2.1C171.5 822 261.2 850 357.8 850 671.4 850 843 590.2 843 364.7c0-7.4 0-14.8-.5-22.2 33.2-24.3 62.3-54.4 85.5-88.2z"
          />
        </svg>
      </span>
      <span>
        Twitter
      </span>
    </span>
    <span
      class="ant-tag ant-tag-checkable css-var-test-id"
    >
      <span
        aria-label="youtube"
        class="anticon anticon-youtube"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="youtube"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M960 509.2c0-2.2 0-4.7-.1-7.6-.1-8.1-.3-17.2-.5-26.9-.8-27.9-2.2-55.7-4.4-81.9-3-36.1-7.4-66.2-13.4-88.8a139.52 139.52 0 00-98.3-98.5c-28.3-7.6-83.7-12.3-161.7-15.2-37.1-1.4-76.8-2.3-116.5-2.8-13.9-.2-26.8-.3-38.4-.4h-29.4c-11.6.1-24.5.2-38.4.4-39.7.5-79.4 1.4-116.5 2.8-78 3-133.5 7.7-161.7 15.2A139.35 139.35 0 0082.4 304C76.3 326.6 72 356.7 69 392.8c-2.2 26.2-3.6 54-4.4 81.9-.3 9.7-.4 18.8-.5 26.9 0 2.9-.1 5.4-.1 7.6v5.6c0 2.2 0 4.7.1 7.6.1 8.1.3 17.2.5 26.9.8 27.9 2.2 55.7 4.4 81.9 3 36.1 7.4 66.2 13.4 88.8 12.8 47.9 50.4 85.7 98.3 98.5 28.2 7.6 83.7 12.3 161.7 15.2 37.1 1.4 76.8 2.3 116.5 2.8 13.9.2 26.8.3 38.4.4h29.4c11.6-.1 24.5-.2 38.4-.4 39.7-.5 79.4-1.4 116.5-2.8 78-3 133.5-7.7 161.7-15.2 47.9-12.8 85.5-50.5 98.3-98.5 6.1-22.6 10.4-52.7 13.4-88.8 2.2-26.2 3.6-54 4.4-81.9.3-9.7.4-18.8.5-26.9 0-2.9.1-5.4.1-7.6v-5.6zm-72 5.2c0 2.1 0 4.4-.1 7.1-.1 7.8-.3 16.4-.5 25.7-.7 26.6-2.1 53.2-4.2 77.9-2.7 32.2-6.5 58.6-11.2 76.3-6.2 23.1-24.4 41.4-47.4 47.5-21 5.6-73.9 10.1-145.8 12.8-36.4 1.4-75.6 2.3-114.7 2.8-13.7.2-26.4.3-37.8.3h-28.6l-37.8-.3c-39.1-.5-78.2-1.4-114.7-2.8-71.9-2.8-124.9-7.2-145.8-12.8-23-6.2-41.2-24.4-47.4-47.5-4.7-17.7-8.5-44.1-11.2-76.3-2.1-24.7-3.4-51.3-4.2-77.9-.3-9.3-.4-18-.5-25.7 0-2.7-.1-5.1-.1-7.1v-4.8c0-2.1 0-4.4.1-7.1.1-7.8.3-16.4.5-25.7.7-26.6 2.1-53.2 4.2-77.9 2.7-32.2 6.5-58.6 11.2-76.3 6.2-23.1 24.4-41.4 47.4-47.5 21-5.6 73.9-10.1 145.8-12.8 36.4-1.4 75.6-2.3 114.7-2.8 13.7-.2 26.4-.3 37.8-.3h28.6l37.8.3c39.1.5 78.2 1.4 114.7 2.8 71.9 2.8 124.9 7.2 145.8 12.8 23 6.2 41.2 24.4 47.4 47.5 4.7 17.7 8.5 44.1 11.2 76.3 2.1 24.7 3.4 51.3 4.2 77.9.3 9.3.4 18 .5 25.7 0 2.7.1 5.1.1 7.1v4.8zM423 646l232-135-232-133z"
          />
        </svg>
      </span>
      <span>
        Youtube
      </span>
    </span>
    <span
      class="ant-tag ant-tag-checkable css-var-test-id"
    >
      <span
        aria-label="facebook"
        class="anticon anticon-facebook"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="facebook"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-32 736H663.9V602.2h104l15.6-120.7H663.9v-77.1c0-35 9.7-58.8 59.8-58.8h63.9v-108c-11.1-1.5-49-4.8-93.2-4.8-92.2 0-155.3 56.3-155.3 159.6v89H434.9v120.7h104.3V848H176V176h672v672z"
          />
        </svg>
      </span>
      <span>
        Facebook
      </span>
    </span>
    <span
      class="ant-tag ant-tag-checkable css-var-test-id"
    >
      <span
        aria-label="linkedin"
        class="anticon anticon-linkedin"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="linkedin"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M847.7 112H176.3c-35.5 0-64.3 28.8-64.3 64.3v671.4c0 35.5 28.8 64.3 64.3 64.3h671.4c35.5 0 64.3-28.8 64.3-64.3V176.3c0-35.5-28.8-64.3-64.3-64.3zm0 736c-447.8-.1-671.7-.2-671.7-.3.1-447.8.2-671.7.3-671.7 447.8.1 671.7.2 671.7.3-.1 447.8-.2 671.7-.3 671.7zM230.6 411.9h118.7v381.8H230.6zm59.4-52.2c37.9 0 68.8-30.8 68.8-68.8a68.8 68.8 0 10-137.6 0c-.1 38 30.7 68.8 68.8 68.8zm252.3 245.1c0-49.8 9.5-98 71.2-98 60.8 0 61.7 56.9 61.7 101.2v185.7h118.6V584.3c0-102.8-22.2-181.9-142.3-181.9-57.7 0-96.4 31.7-112.3 61.7h-1.6v-52.2H423.7v381.8h118.6V604.8z"
          />
        </svg>
      </span>
      <span>
        LinkedIn
      </span>
    </span>
  </div>,
]
`;

exports[`renders components/tag/demo/icon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/status.tsx extend context correctly 1`] = `
Array [
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Status (filled)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-filled ant-tag-success css-var-test-id"
      >
        <span
          aria-label="check-circle"
          class="anticon anticon-check-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="check-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
            />
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
          </svg>
        </span>
        <span>
          success
        </span>
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-processing css-var-test-id"
      >
        <span
          aria-label="sync"
          class="anticon anticon-sync anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="sync"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"
            />
          </svg>
        </span>
        <span>
          processing
        </span>
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-warning css-var-test-id"
      >
        <span
          aria-label="exclamation-circle"
          class="anticon anticon-exclamation-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="exclamation-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        <span>
          warning
        </span>
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-error css-var-test-id"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
            />
          </svg>
        </span>
        <span>
          error
        </span>
      </span>
      <span
        class="ant-tag ant-tag-filled ant-tag-default css-var-test-id"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
        <span>
          default
        </span>
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Status (solid)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-solid ant-tag-success css-var-test-id"
      >
        <span
          aria-label="check-circle"
          class="anticon anticon-check-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="check-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
            />
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
          </svg>
        </span>
        <span>
          success
        </span>
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-processing css-var-test-id"
      >
        <span
          aria-label="sync"
          class="anticon anticon-sync anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="sync"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"
            />
          </svg>
        </span>
        <span>
          processing
        </span>
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-warning css-var-test-id"
      >
        <span
          aria-label="exclamation-circle"
          class="anticon anticon-exclamation-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="exclamation-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        <span>
          warning
        </span>
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-error css-var-test-id"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
            />
          </svg>
        </span>
        <span>
          error
        </span>
      </span>
      <span
        class="ant-tag ant-tag-solid ant-tag-default css-var-test-id"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
        <span>
          default
        </span>
      </span>
    </div>
  </div>,
  <div>
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-with-text ant-divider-with-text-start"
      role="separator"
    >
      <div
        class="ant-divider-rail ant-divider-rail-start"
      />
      <span
        class="ant-divider-inner-text"
      >
        Status (outlined)
      </span>
      <div
        class="ant-divider-rail ant-divider-rail-end"
      />
    </div>
    <div
      class="ant-flex css-var-test-id ant-flex-wrap-wrap ant-flex-align-center ant-flex-gap-small"
    >
      <span
        class="ant-tag ant-tag-outlined ant-tag-success css-var-test-id"
      >
        <span
          aria-label="check-circle"
          class="anticon anticon-check-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="check-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
            />
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
          </svg>
        </span>
        <span>
          success
        </span>
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-processing css-var-test-id"
      >
        <span
          aria-label="sync"
          class="anticon anticon-sync anticon-spin"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="sync"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"
            />
          </svg>
        </span>
        <span>
          processing
        </span>
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-warning css-var-test-id"
      >
        <span
          aria-label="exclamation-circle"
          class="anticon anticon-exclamation-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="exclamation-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
            />
          </svg>
        </span>
        <span>
          warning
        </span>
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-error css-var-test-id"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
            />
          </svg>
        </span>
        <span>
          error
        </span>
      </span>
      <span
        class="ant-tag ant-tag-outlined ant-tag-default css-var-test-id"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
        <span>
          default
        </span>
      </span>
    </div>
  </div>,
]
`;

exports[`renders components/tag/demo/status.tsx extend context correctly 2`] = `[]`;

exports[`renders components/tag/demo/style-class.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-large ant-space-gap-col-large css-var-test-id"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        对象形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag custom-tag-root ant-tag-filled css-var-test-id"
            style="background-color: rgb(240, 248, 255); border: 2px solid rgb(24, 144, 255); border-radius: 8px; padding: 4px 12px;"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle custom-tag-icon"
              role="img"
              style="color: rgb(82, 196, 26); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="custom-tag-content"
              style="font-weight: bold; color: rgb(24, 144, 255);"
            >
              成功标签
            </span>
          </span>
        </div>
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag custom-error-tag ant-tag-filled ant-tag-red css-var-test-id"
            style="background-color: rgb(255, 242, 240); border: 2px solid rgb(255, 77, 79); border-radius: 12px; padding: 6px 16px; box-shadow: 0 2px 8px rgba(255, 77, 79, 0.2);"
          >
            <span
              aria-label="close-circle"
              class="anticon anticon-close-circle custom-error-icon"
              role="img"
              style="color: rgb(255, 77, 79); font-size: 14px;"
            >
              <svg
                aria-hidden="true"
                data-icon="close-circle"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
            <span
              class="custom-error-content"
              style="color: rgb(255, 77, 79); font-weight: 500;"
            >
              错误标签
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
      role="separator"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        函数形式的 classNames 和 styles
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag filled-tag ant-tag-filled ant-tag-blue css-var-test-id"
            style="background-color: rgb(230, 247, 255); border: 2px solid rgb(24, 144, 255); border-radius: 16px; padding: 8px 16px; transform: scale(1.05);"
          >
            <span
              aria-label="sync"
              class="anticon anticon-sync icon-blue"
              role="img"
              style="color: rgb(24, 144, 255); font-size: 18px; margin-right: 8px;"
            >
              <svg
                aria-hidden="true"
                data-icon="sync"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"
                />
              </svg>
            </span>
            <span
              class="content-filled"
              style="color: rgb(24, 144, 255); font-weight: bold;"
            >
              动态样式标签
            </span>
          </span>
        </div>
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag tag-outlined-enabled ant-tag-outlined ant-tag-orange css-var-test-id"
            style="background-color: rgb(255, 247, 230); border: 2px dashed rgb(250, 140, 22); border-radius: 6px; padding: 6px 14px; opacity: 1;"
          >
            <span
              aria-label="exclamation-circle"
              class="anticon anticon-exclamation-circle icon-orange-bright"
              role="img"
              style="color: rgb(250, 140, 22); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="exclamation-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
                <path
                  d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
                />
              </svg>
            </span>
            <span
              class="dynamic-content"
              style="color: rgb(250, 140, 22); font-style: normal;"
            >
              警告标签
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
      role="separator"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        不同变体的标签样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag variant-filled ant-tag-filled ant-tag-purple css-var-test-id"
            style="background-color: rgb(249, 240, 255); border: 2px solid rgb(114, 46, 209); color: rgb(114, 46, 209); border-radius: 8px; padding: 8px 16px; font-weight: bold;"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle icon-variant-filled"
              role="img"
              style="color: rgb(114, 46, 209); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="content-variant-filled"
              style="color: rgb(114, 46, 209);"
            >
              filled 标签
            </span>
          </span>
        </div>
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag variant-solid ant-tag-solid ant-tag-purple css-var-test-id"
            style="background-color: rgb(114, 46, 209); border: 2px solid rgb(114, 46, 209); color: rgb(255, 255, 255); border-radius: 8px; padding: 8px 16px; font-weight: bold;"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle icon-variant-solid"
              role="img"
              style="color: rgb(255, 255, 255); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="content-variant-solid"
              style="color: rgb(255, 255, 255);"
            >
              solid 标签
            </span>
          </span>
        </div>
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag variant-outlined ant-tag-outlined ant-tag-purple css-var-test-id"
            style="background-color: transparent; border: 2px solid rgb(114, 46, 209); color: rgb(114, 46, 209); border-radius: 8px; padding: 8px 16px; font-weight: bold;"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle icon-variant-outlined"
              role="img"
              style="color: rgb(114, 46, 209); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="content-variant-outlined"
              style="color: rgb(114, 46, 209);"
            >
              outlined 标签
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
      role="separator"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        可关闭标签的样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag closable-tag ant-tag-filled ant-tag-green css-var-test-id"
            style="background-color: rgb(246, 255, 237); border: 2px solid rgb(82, 196, 26); border-radius: 20px; padding: 6px 32px 6px 16px; position: relative;"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle closable-icon"
              role="img"
              style="color: rgb(82, 196, 26); font-size: 14px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="closable-content"
              style="color: rgb(82, 196, 26); font-weight: 500;"
            >
              可关闭标签
            </span>
            <span
              aria-label="Close"
              class="anticon anticon-close ant-tag-close-icon"
              role="img"
              tabindex="-1"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-space-item"
        >
          <span
            class="ant-tag solid-closable-red ant-tag-solid ant-tag-red css-var-test-id"
            style="background-color: rgb(255, 77, 79); border-radius: 4px; padding: 8px 32px 8px 16px; color: rgb(255, 255, 255);"
          >
            <span
              aria-label="exclamation-circle"
              class="anticon anticon-exclamation-circle solid-icon"
              role="img"
              style="color: rgb(255, 255, 255); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="exclamation-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
                <path
                  d="M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
                />
              </svg>
            </span>
            <span
              class="solid-content"
              style="color: rgb(255, 255, 255); font-weight: bold;"
            >
              实心可关闭
            </span>
            <span
              aria-label="Close"
              class="anticon anticon-close ant-tag-close-icon"
              role="img"
              tabindex="-1"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-divider css-var-test-id ant-divider-horizontal ant-divider-rail"
      role="separator"
    />
  </div>
  <div
    class="ant-space-item"
  >
    <div>
      <h4>
        链接标签的样式
      </h4>
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small css-var-test-id"
        style="flex-wrap: wrap;"
      >
        <div
          class="ant-space-item"
        >
          <a
            class="ant-tag link-tag ant-tag-filled ant-tag-blue css-var-test-id"
            href="https://ant.design"
            style="background-color: rgb(230, 247, 255); border: 2px solid rgb(24, 144, 255); border-radius: 8px; padding: 8px 16px; text-decoration: none; cursor: pointer; transition: all 0.3s ease;"
            target="_blank"
          >
            <span
              aria-label="check-circle"
              class="anticon anticon-check-circle link-icon"
              role="img"
              style="color: rgb(24, 144, 255); font-size: 16px;"
            >
              <svg
                aria-hidden="true"
                data-icon="check-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"
                />
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
              </svg>
            </span>
            <span
              class="link-content"
              style="color: rgb(24, 144, 255); font-weight: 500;"
            >
              链接标签
            </span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`renders components/tag/demo/style-class.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Space] \`direction\` is deprecated. Please use \`orientation\` instead.",
]
`;
