## zh-CN

通过 `classNames` 和 `styles` 属性为 Tag 组件的各个部分设置样式。

- `classNames` 和 `styles` 支持对象和函数两种形式
- 对象形式：直接设置各个语义化 DOM 的类名和样式
- 函数形式：`(info: { props }) => Record<SemanticDOM, string | CSSProperties>`，可以根据组件的 props 动态设置样式

### 语义化 DOM 结构

- `root`：标签根容器
- `icon`：标签图标
- `content`：标签内容文本

## en-US

Set styles for different parts of the Tag component through `classNames` and `styles` properties.

- `classNames` and `styles` support both object and function forms
- Object form: directly set class names and styles for each semantic DOM
- Function form: `(info: { props }) => Record<SemanticDOM, string | CSSProperties>`, can dynamically set styles based on component props

### Semantic DOM Structure

- `root`: Tag root container
- `icon`: Tag icon
- `content`: Tag content text
