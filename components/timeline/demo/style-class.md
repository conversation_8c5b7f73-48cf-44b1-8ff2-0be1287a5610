---
order: 99
title:
  zh-CN: 语义化样式
  en-US: Semantic Style
debug: true
---

## zh-CN

通过 `classNames` 和 `styles` 属性为 Timeline 的各个语义化部分设置样式。

### 语义化 DOM 结构

- `root`：根元素，时间轴容器
- `item`：节点元素，单个时间节点
- `itemWrapper`：节点包装元素，时间节点内容的包装容器
- `itemIcon`：节点图标元素，节点头部图标
- `itemSection`：节点区域元素，包含头部和内容的区域容器
- `itemHeader`：节点头部元素，包含标题和连接线的头部区域
- `itemTitle`：节点标题元素，节点标题文字
- `itemContent`：节点内容元素，节点详细内容
- `itemRail`：节点连接线元素，连接时间节点的轨道线条

## en-US

Use `classNames` and `styles` properties to set styles for various semantic parts of Timeline.

### Semantic DOM Structure

- `root`: Root element, timeline container
- `item`: Item element, single timeline node
- `itemWrapper`: Item wrapper element, wrapping container for timeline node content
- `itemIcon`: Item icon element, node head icon
- `itemSection`: Item section element, section container containing header and content
- `itemHeader`: Item header element, header area containing title and rail
- `itemTitle`: Item title element, node title text
- `itemContent`: Item content element, node detail content
- `itemRail`: Item rail element, track line connecting timeline nodes
